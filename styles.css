/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    z-index: 1000;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4f46e5;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.8rem;
}

/* 搜索框样式 */
.search-container {
    flex: 1;
    max-width: 600px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #3730a3;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
}

/* 图谱容器 */
.graph-container {
    flex: 1;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.graph-canvas {
    width: 100%;
    height: 100%;
    position: relative;
}

/* 欢迎消息 */
.welcome-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 10;
}

.welcome-content i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.welcome-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.welcome-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.example-topics {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.topic-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.topic-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 图谱控制面板 */
.graph-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 100;
}

.control-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
    background: white;
    transform: scale(1.05);
}

/* 侧边栏样式 */
.sidebar {
    width: 400px;
    background: white;
    border-left: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 200;
}

.sidebar.open {
    transform: translateX(0);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0.25rem;
}

.close-btn:hover {
    color: #374151;
}

.sidebar-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.empty-state {
    text-align: center;
    color: #6b7280;
    margin-top: 2rem;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: #ef4444;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    display: none;
    align-items: center;
    gap: 0.5rem;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-toast.show {
    display: flex;
}

.close-error {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    margin-left: auto;
}

/* 侧边栏详细样式 */
.concept-details {
    line-height: 1.6;
}

.concept-header {
    margin-bottom: 1.5rem;
}

.concept-type {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-main_concept { background: #4f46e5; color: white; }
.type-sub_concept { background: #7c3aed; color: white; }
.type-related_concept { background: #06b6d4; color: white; }
.type-philosopher { background: #10b981; color: white; }
.type-work { background: #f59e0b; color: white; }

.importance-score {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.concept-description,
.key-points,
.related-philosophers,
.related-works,
.concept-connections {
    margin-bottom: 1.5rem;
}

.concept-details h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.concept-details h4 i {
    color: #4f46e5;
}

.concept-description p {
    color: #4b5563;
    margin-bottom: 0;
}

.key-points ul {
    list-style: none;
    padding: 0;
}

.key-points li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
    color: #4b5563;
    position: relative;
    padding-left: 1.5rem;
}

.key-points li:before {
    content: '•';
    color: #4f46e5;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.key-points li:last-child {
    border-bottom: none;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    padding: 0.375rem 0.75rem;
    border-radius: 16px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.philosopher-tag {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.philosopher-tag:hover {
    background: #bbf7d0;
}

.work-tag {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.work-tag:hover {
    background: #fde68a;
}

.connections-list {
    max-height: 200px;
    overflow-y: auto;
}

.connection-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.connection-item:hover {
    background: #f9fafb;
    border-color: #4f46e5;
}

.connection-direction {
    font-weight: bold;
    color: #4f46e5;
    font-size: 1.1rem;
}

.connection-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background: #f3f4f6;
    border-radius: 12px;
    color: #6b7280;
    font-weight: 500;
}

.connection-target {
    font-weight: 500;
    color: #374151;
    flex: 1;
}

.no-connections {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.action-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn.primary {
    background: #4f46e5;
    color: white;
}

.action-btn.primary:hover {
    background: #3730a3;
}

.action-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.action-btn.secondary:hover {
    background: #e5e7eb;
}

.sidebar-message {
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.sidebar-message.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.sidebar-message.warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.sidebar-message.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .sidebar {
        width: 100%;
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
    }

    .graph-controls {
        flex-direction: row;
        top: auto;
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        justify-content: center;
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }

    .example-topics {
        flex-direction: column;
        align-items: center;
    }

    .action-buttons {
        flex-direction: column;
    }

    .concept-type {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
