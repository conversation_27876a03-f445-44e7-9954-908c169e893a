<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哲学知识图谱学习网站</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-brain"></i>
                    哲学知识图谱
                </h1>
                <div class="search-container">
                    <div class="search-box">
                        <input 
                            type="text" 
                            id="searchInput" 
                            placeholder="输入哲学主题，如：存在主义、道德哲学、自由意志..."
                            class="search-input"
                        >
                        <button id="searchBtn" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 知识图谱可视化区域 -->
            <div class="graph-container">
                <div id="knowledge-graph" class="graph-canvas">
                    <div class="welcome-message">
                        <div class="welcome-content">
                            <i class="fas fa-lightbulb"></i>
                            <h2>探索哲学的智慧世界</h2>
                            <p>输入任何哲学主题，AI将为您生成概念网络图谱</p>
                            <div class="example-topics">
                                <span class="topic-tag" data-topic="存在主义">存在主义</span>
                                <span class="topic-tag" data-topic="道德哲学">道德哲学</span>
                                <span class="topic-tag" data-topic="自由意志">自由意志</span>
                                <span class="topic-tag" data-topic="认识论">认识论</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 图谱控制面板 -->
                <div class="graph-controls">
                    <button id="zoomIn" class="control-btn" title="放大">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button id="zoomOut" class="control-btn" title="缩小">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button id="resetView" class="control-btn" title="重置视图">
                        <i class="fas fa-home"></i>
                    </button>
                    <button id="fullscreen" class="control-btn" title="全屏">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>

            <!-- 侧边栏详情面板 -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3 id="sidebarTitle">概念详情</h3>
                    <button id="closeSidebar" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="sidebar-content" id="sidebarContent">
                    <div class="empty-state">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>点击图谱中的概念节点查看详细信息</p>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 加载状态 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>AI正在分析概念关系...</p>
            </div>
        </div>

        <!-- 错误提示 -->
        <div class="error-toast" id="errorToast">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage"></span>
            <button id="closeError" class="close-error">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="js/config.js"></script>
    <script src="js/ai-service.js"></script>
    <script src="js/graph-visualization.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
