// 应用配置文件
const CONFIG = {
    // AI服务配置
    AI_SERVICE: {
        // 这里可以配置不同的AI服务提供商
        // 由于是演示版本，我们将使用模拟数据
        USE_MOCK_DATA: true,
        
        // 如果要使用真实的AI服务，可以在这里配置API密钥
        // OPENAI_API_KEY: 'your-api-key-here',
        // API_ENDPOINT: 'https://api.openai.com/v1/chat/completions'
    },
    
    // 图谱可视化配置
    GRAPH: {
        WIDTH: 800,
        HEIGHT: 600,
        NODE_RADIUS: {
            MIN: 15,
            MAX: 40,
            DEFAULT: 25
        },
        LINK_DISTANCE: 100,
        CHARGE_STRENGTH: -300,
        COLORS: {
            PRIMARY: '#4f46e5',
            SECONDARY: '#7c3aed',
            ACCENT: '#06b6d4',
            SUCCESS: '#10b981',
            WARNING: '#f59e0b',
            DANGER: '#ef4444'
        },
        ANIMATION_DURATION: 1000
    },
    
    // 节点类型配置
    NODE_TYPES: {
        MAIN_CONCEPT: {
            color: '#4f46e5',
            radius: 35,
            strokeWidth: 3
        },
        SUB_CONCEPT: {
            color: '#7c3aed',
            radius: 25,
            strokeWidth: 2
        },
        RELATED_CONCEPT: {
            color: '#06b6d4',
            radius: 20,
            strokeWidth: 1
        },
        PHILOSOPHER: {
            color: '#10b981',
            radius: 22,
            strokeWidth: 2
        },
        WORK: {
            color: '#f59e0b',
            radius: 18,
            strokeWidth: 1
        }
    },
    
    // 链接类型配置
    LINK_TYPES: {
        CONTAINS: {
            color: '#6b7280',
            strokeWidth: 2,
            strokeDasharray: 'none'
        },
        RELATES_TO: {
            color: '#9ca3af',
            strokeWidth: 1,
            strokeDasharray: '5,5'
        },
        OPPOSES: {
            color: '#ef4444',
            strokeWidth: 2,
            strokeDasharray: '10,5'
        },
        INFLUENCES: {
            color: '#10b981',
            strokeWidth: 1.5,
            strokeDasharray: 'none'
        }
    },
    
    // 侧边栏配置
    SIDEBAR: {
        WIDTH: 400,
        ANIMATION_DURATION: 300
    },
    
    // 搜索配置
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        DEBOUNCE_DELAY: 300
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        NETWORK_ERROR: '网络连接失败，请检查您的网络连接',
        AI_SERVICE_ERROR: 'AI服务暂时不可用，请稍后重试',
        INVALID_INPUT: '请输入有效的哲学主题',
        GENERAL_ERROR: '发生了未知错误，请刷新页面重试'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        GRAPH_GENERATED: '知识图谱生成成功',
        CONCEPT_LOADED: '概念详情加载完成'
    }
};

// 导出配置（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
