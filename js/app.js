// 主应用文件 - 协调各个模块
class PhilosophyApp {
    constructor() {
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.errorToast = document.getElementById('errorToast');
        this.errorMessage = document.getElementById('errorMessage');
        this.closeErrorBtn = document.getElementById('closeError');
        
        this.currentTopic = null;
        this.searchTimeout = null;
        
        this.initializeApp();
        this.setupEventListeners();
    }
    
    // 初始化应用
    initializeApp() {
        // 初始化图谱可视化
        window.graphVisualization = new GraphVisualization('knowledge-graph');
        
        // 设置全局引用
        window.sidebar = sidebar;
        
        console.log('哲学知识图谱应用初始化完成');
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 搜索按钮点击
        this.searchBtn.addEventListener('click', () => {
            this.handleSearch();
        });
        
        // 搜索框回车
        this.searchInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.handleSearch();
            }
        });
        
        // 搜索框实时输入（防抖）
        this.searchInput.addEventListener('input', () => {
            this.handleSearchInput();
        });
        
        // 示例主题标签点击
        document.querySelectorAll('.topic-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                const topic = tag.getAttribute('data-topic');
                this.searchInput.value = topic;
                this.handleSearch();
            });
        });
        
        // 图谱控制按钮
        document.getElementById('zoomIn').addEventListener('click', () => {
            if (window.graphVisualization) {
                window.graphVisualization.zoomIn();
            }
        });
        
        document.getElementById('zoomOut').addEventListener('click', () => {
            if (window.graphVisualization) {
                window.graphVisualization.zoomOut();
            }
        });
        
        document.getElementById('resetView').addEventListener('click', () => {
            if (window.graphVisualization) {
                window.graphVisualization.resetView();
            }
        });
        
        document.getElementById('fullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // 错误提示关闭
        this.closeErrorBtn.addEventListener('click', () => {
            this.hideError();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });
    }
    
    // 处理搜索输入（防抖）
    handleSearchInput() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            const query = this.searchInput.value.trim();
            if (query.length >= CONFIG.SEARCH.MIN_QUERY_LENGTH) {
                // 这里可以添加搜索建议功能
                this.showSearchSuggestions(query);
            }
        }, CONFIG.SEARCH.DEBOUNCE_DELAY);
    }
    
    // 处理搜索
    async handleSearch() {
        const topic = this.searchInput.value.trim();
        
        if (!topic) {
            this.showError('请输入哲学主题');
            return;
        }
        
        if (topic.length < CONFIG.SEARCH.MIN_QUERY_LENGTH) {
            this.showError(CONFIG.ERROR_MESSAGES.INVALID_INPUT);
            return;
        }
        
        try {
            // 隐藏之前的错误
            this.hideError();
            
            // 关闭侧边栏
            if (sidebar.isOpen()) {
                sidebar.hide();
            }
            
            // 调用AI服务分析主题
            const analysisResult = await aiService.analyzePhilosophicalTopic(topic);
            
            // 渲染知识图谱
            this.renderKnowledgeGraph(analysisResult);
            
            // 保存当前主题
            this.currentTopic = topic;
            
            // 添加到搜索历史
            this.addToSearchHistory(topic);
            
            console.log('搜索完成:', topic, analysisResult);
            
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError(error.message || CONFIG.ERROR_MESSAGES.GENERAL_ERROR);
        }
    }
    
    // 渲染知识图谱
    renderKnowledgeGraph(data) {
        if (!window.graphVisualization) {
            console.error('图谱可视化组件未初始化');
            return;
        }
        
        // 渲染图谱
        window.graphVisualization.renderGraph(data);
        
        // 设置侧边栏数据
        sidebar.setCurrentData(data);
        
        // 更新页面标题
        document.title = `${data.mainConcept} - 哲学知识图谱`;
    }
    
    // 显示搜索建议
    showSearchSuggestions(query) {
        // 这里可以实现搜索建议功能
        // 暂时留空，后续可以添加
    }
    
    // 添加到搜索历史
    addToSearchHistory(topic) {
        try {
            const history = JSON.parse(localStorage.getItem('philosophy_search_history') || '[]');
            
            // 移除重复项
            const filteredHistory = history.filter(item => item !== topic);
            
            // 添加到开头
            filteredHistory.unshift(topic);
            
            // 限制历史记录数量
            const limitedHistory = filteredHistory.slice(0, 10);
            
            localStorage.setItem('philosophy_search_history', JSON.stringify(limitedHistory));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }
    
    // 获取搜索历史
    getSearchHistory() {
        try {
            return JSON.parse(localStorage.getItem('philosophy_search_history') || '[]');
        } catch (error) {
            console.warn('获取搜索历史失败:', error);
            return [];
        }
    }
    
    // 切换全屏
    toggleFullscreen() {
        const graphContainer = document.querySelector('.graph-container');
        
        if (!document.fullscreenElement) {
            graphContainer.requestFullscreen().catch(err => {
                console.warn('无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // 处理键盘快捷键
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K: 聚焦搜索框
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            this.searchInput.focus();
            this.searchInput.select();
        }
        
        // Ctrl/Cmd + Enter: 执行搜索
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            this.handleSearch();
        }
        
        // F11: 切换全屏
        if (event.key === 'F11') {
            event.preventDefault();
            this.toggleFullscreen();
        }
    }
    
    // 显示错误信息
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorToast.classList.add('show');
        
        // 5秒后自动隐藏
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }
    
    // 隐藏错误信息
    hideError() {
        this.errorToast.classList.remove('show');
    }
    
    // 清除当前图谱
    clearGraph() {
        if (window.graphVisualization) {
            window.graphVisualization.clear();
        }
        
        if (sidebar.isOpen()) {
            sidebar.hide();
        }
        
        this.currentTopic = null;
        document.title = '哲学知识图谱学习网站';
    }
    
    // 导出图谱数据
    exportGraphData() {
        if (!this.currentTopic || !sidebar.currentData) {
            this.showError('没有可导出的数据');
            return;
        }
        
        try {
            const dataStr = JSON.stringify(sidebar.currentData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `${this.currentTopic}_knowledge_graph.json`;
            link.click();
            
            URL.revokeObjectURL(link.href);
        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败，请重试');
        }
    }
    
    // 获取应用状态
    getAppState() {
        return {
            currentTopic: this.currentTopic,
            searchHistory: this.getSearchHistory(),
            sidebarOpen: sidebar.isOpen(),
            hasGraphData: !!sidebar.currentData
        };
    }
}

// 应用初始化
document.addEventListener('DOMContentLoaded', () => {
    // 创建全局应用实例
    window.philosophyApp = new PhilosophyApp();
    
    console.log('哲学知识图谱应用启动完成');
});
