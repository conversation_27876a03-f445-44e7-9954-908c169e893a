// AI服务模块 - 负责概念分析和知识图谱生成
class AIService {
    constructor() {
        this.mockData = this.initializeMockData();
    }
    
    // 初始化模拟数据
    initializeMockData() {
        return {
            '存在主义': {
                mainConcept: '存在主义',
                description: '存在主义是一种哲学思潮，强调个体存在、自由选择和个人责任。它认为存在先于本质，人必须为自己的选择承担责任。',
                nodes: [
                    { id: 'existentialism', name: '存在主义', type: 'MAIN_CONCEPT', importance: 1.0 },
                    { id: 'existence', name: '存在', type: 'SUB_CONCEPT', importance: 0.9 },
                    { id: 'essence', name: '本质', type: 'SUB_CONCEPT', importance: 0.8 },
                    { id: 'freedom', name: '自由', type: 'SUB_CONCEPT', importance: 0.9 },
                    { id: 'responsibility', name: '责任', type: 'SUB_CONCEPT', importance: 0.8 },
                    { id: 'authenticity', name: '真实性', type: 'SUB_CONCEPT', importance: 0.7 },
                    { id: 'anxiety', name: '焦虑', type: 'RELATED_CONCEPT', importance: 0.6 },
                    { id: 'sartre', name: '萨特', type: 'PHILOSOPHER', importance: 0.9 },
                    { id: 'camus', name: '加缪', type: 'PHILOSOPHER', importance: 0.8 },
                    { id: 'kierkegaard', name: '克尔凯郭尔', type: 'PHILOSOPHER', importance: 0.8 },
                    { id: 'being_nothingness', name: '存在与虚无', type: 'WORK', importance: 0.7 },
                    { id: 'stranger', name: '局外人', type: 'WORK', importance: 0.6 }
                ],
                links: [
                    { source: 'existentialism', target: 'existence', type: 'CONTAINS' },
                    { source: 'existentialism', target: 'essence', type: 'CONTAINS' },
                    { source: 'existentialism', target: 'freedom', type: 'CONTAINS' },
                    { source: 'existentialism', target: 'responsibility', type: 'CONTAINS' },
                    { source: 'freedom', target: 'responsibility', type: 'RELATES_TO' },
                    { source: 'existence', target: 'essence', type: 'OPPOSES' },
                    { source: 'existentialism', target: 'authenticity', type: 'CONTAINS' },
                    { source: 'authenticity', target: 'anxiety', type: 'RELATES_TO' },
                    { source: 'sartre', target: 'existentialism', type: 'INFLUENCES' },
                    { source: 'camus', target: 'existentialism', type: 'INFLUENCES' },
                    { source: 'kierkegaard', target: 'existentialism', type: 'INFLUENCES' },
                    { source: 'sartre', target: 'being_nothingness', type: 'RELATES_TO' },
                    { source: 'camus', target: 'stranger', type: 'RELATES_TO' }
                ],
                details: {
                    'existentialism': {
                        title: '存在主义',
                        description: '存在主义是20世纪的一种哲学思潮，强调个体存在的独特性和主观性。',
                        keyPoints: [
                            '存在先于本质',
                            '个体自由选择',
                            '承担个人责任',
                            '追求真实的自我'
                        ],
                        philosophers: ['萨特', '加缪', '克尔凯郭尔', '海德格尔'],
                        works: ['存在与虚无', '局外人', '恶心', '存在与时间']
                    },
                    'freedom': {
                        title: '自由',
                        description: '在存在主义中，自由是人类存在的基本特征，意味着人有选择自己行为和价值观的能力。',
                        keyPoints: [
                            '绝对的选择自由',
                            '自由带来焦虑',
                            '自由与责任相伴',
                            '自由是人的宿命'
                        ],
                        philosophers: ['萨特', '波伏娃'],
                        works: ['存在与虚无', '第二性']
                    }
                }
            },
            '道德哲学': {
                mainConcept: '道德哲学',
                description: '道德哲学研究什么是对的、什么是错的，以及我们应该如何生活的问题。',
                nodes: [
                    { id: 'ethics', name: '道德哲学', type: 'MAIN_CONCEPT', importance: 1.0 },
                    { id: 'virtue_ethics', name: '德性伦理学', type: 'SUB_CONCEPT', importance: 0.9 },
                    { id: 'deontology', name: '义务伦理学', type: 'SUB_CONCEPT', importance: 0.9 },
                    { id: 'consequentialism', name: '后果主义', type: 'SUB_CONCEPT', importance: 0.9 },
                    { id: 'utilitarianism', name: '功利主义', type: 'RELATED_CONCEPT', importance: 0.8 },
                    { id: 'categorical_imperative', name: '绝对命令', type: 'RELATED_CONCEPT', importance: 0.8 },
                    { id: 'aristotle', name: '亚里士多德', type: 'PHILOSOPHER', importance: 0.9 },
                    { id: 'kant', name: '康德', type: 'PHILOSOPHER', importance: 0.9 },
                    { id: 'mill', name: '密尔', type: 'PHILOSOPHER', importance: 0.8 },
                    { id: 'nicomachean_ethics', name: '尼各马可伦理学', type: 'WORK', importance: 0.8 }
                ],
                links: [
                    { source: 'ethics', target: 'virtue_ethics', type: 'CONTAINS' },
                    { source: 'ethics', target: 'deontology', type: 'CONTAINS' },
                    { source: 'ethics', target: 'consequentialism', type: 'CONTAINS' },
                    { source: 'consequentialism', target: 'utilitarianism', type: 'CONTAINS' },
                    { source: 'deontology', target: 'categorical_imperative', type: 'CONTAINS' },
                    { source: 'aristotle', target: 'virtue_ethics', type: 'INFLUENCES' },
                    { source: 'kant', target: 'deontology', type: 'INFLUENCES' },
                    { source: 'mill', target: 'utilitarianism', type: 'INFLUENCES' },
                    { source: 'aristotle', target: 'nicomachean_ethics', type: 'RELATES_TO' }
                ],
                details: {
                    'ethics': {
                        title: '道德哲学',
                        description: '道德哲学是哲学的一个分支，研究道德价值、道德判断和道德行为的本质。',
                        keyPoints: [
                            '什么是善恶',
                            '道德判断的标准',
                            '道德行为的动机',
                            '道德责任的界定'
                        ],
                        philosophers: ['亚里士多德', '康德', '密尔', '尼采'],
                        works: ['尼各马可伦理学', '道德形而上学基础', '功利主义', '善恶的彼岸']
                    }
                }
            }
        };
    }
    
    // 分析哲学主题并生成知识图谱数据
    async analyzePhilosophicalTopic(topic) {
        try {
            // 显示加载状态
            this.showLoading();
            
            // 模拟API调用延迟
            await this.delay(1500);
            
            if (CONFIG.AI_SERVICE.USE_MOCK_DATA) {
                return this.getMockAnalysis(topic);
            } else {
                return await this.callRealAIService(topic);
            }
        } catch (error) {
            console.error('AI分析失败:', error);
            throw new Error(CONFIG.ERROR_MESSAGES.AI_SERVICE_ERROR);
        } finally {
            this.hideLoading();
        }
    }
    
    // 获取模拟分析数据
    getMockAnalysis(topic) {
        // 检查是否有预定义的数据
        if (this.mockData[topic]) {
            return this.mockData[topic];
        }
        
        // 生成通用的分析结果
        return this.generateGenericAnalysis(topic);
    }
    
    // 生成通用分析结果
    generateGenericAnalysis(topic) {
        const concepts = this.generateRelatedConcepts(topic);
        const philosophers = this.generateRelatedPhilosophers(topic);
        const works = this.generateRelatedWorks(topic);
        
        const nodes = [
            { id: 'main', name: topic, type: 'MAIN_CONCEPT', importance: 1.0 },
            ...concepts.map((concept, index) => ({
                id: `concept_${index}`,
                name: concept,
                type: 'SUB_CONCEPT',
                importance: 0.8 - index * 0.1
            })),
            ...philosophers.map((philosopher, index) => ({
                id: `philosopher_${index}`,
                name: philosopher,
                type: 'PHILOSOPHER',
                importance: 0.7 - index * 0.1
            })),
            ...works.map((work, index) => ({
                id: `work_${index}`,
                name: work,
                type: 'WORK',
                importance: 0.6 - index * 0.1
            }))
        ];
        
        const links = this.generateLinks(nodes);
        
        return {
            mainConcept: topic,
            description: `${topic}是哲学中的重要概念，涉及多个相关理论和思想家的贡献。`,
            nodes,
            links,
            details: this.generateDetails(nodes)
        };
    }
    
    // 生成相关概念
    generateRelatedConcepts(topic) {
        const conceptMap = {
            '自由意志': ['决定论', '因果关系', '道德责任', '选择'],
            '认识论': ['知识', '真理', '信念', '怀疑主义'],
            '形而上学': ['存在', '实在', '本质', '因果性'],
            '美学': ['美', '艺术', '审美经验', '创造性']
        };
        
        return conceptMap[topic] || ['相关概念1', '相关概念2', '相关概念3'];
    }
    
    // 生成相关哲学家
    generateRelatedPhilosophers(topic) {
        const philosopherMap = {
            '自由意志': ['康德', '休谟', '斯宾诺莎'],
            '认识论': ['笛卡尔', '洛克', '康德'],
            '形而上学': ['亚里士多德', '柏拉图', '海德格尔'],
            '美学': ['康德', '黑格尔', '叔本华']
        };
        
        return philosopherMap[topic] || ['相关哲学家1', '相关哲学家2'];
    }
    
    // 生成相关著作
    generateRelatedWorks(topic) {
        return ['相关著作1', '相关著作2'];
    }
    
    // 生成链接关系
    generateLinks(nodes) {
        const links = [];
        const mainNode = nodes[0];
        
        // 主概念连接到子概念
        nodes.slice(1).forEach(node => {
            if (node.type === 'SUB_CONCEPT') {
                links.push({
                    source: mainNode.id,
                    target: node.id,
                    type: 'CONTAINS'
                });
            } else if (node.type === 'PHILOSOPHER') {
                links.push({
                    source: node.id,
                    target: mainNode.id,
                    type: 'INFLUENCES'
                });
            } else if (node.type === 'WORK') {
                links.push({
                    source: mainNode.id,
                    target: node.id,
                    type: 'RELATES_TO'
                });
            }
        });
        
        return links;
    }
    
    // 生成详细信息
    generateDetails(nodes) {
        const details = {};
        nodes.forEach(node => {
            details[node.id] = {
                title: node.name,
                description: `${node.name}的详细描述和解释。`,
                keyPoints: ['要点1', '要点2', '要点3'],
                philosophers: ['相关哲学家'],
                works: ['相关著作']
            };
        });
        return details;
    }
    
    // 调用真实的AI服务（预留接口）
    async callRealAIService(topic) {
        // 这里可以集成真实的AI API
        throw new Error('真实AI服务集成待实现');
    }
    
    // 显示加载状态
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('show');
        }
    }
    
    // 隐藏加载状态
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
        }
    }
    
    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 创建全局AI服务实例
const aiService = new AIService();
