// 知识图谱可视化模块
class GraphVisualization {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = d3.select(`#${containerId}`);
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.zoom = null;
        this.transform = d3.zoomIdentity;
        
        this.initializeGraph();
        this.setupEventListeners();
    }
    
    // 初始化图谱
    initializeGraph() {
        // 清除现有内容
        this.container.selectAll('*').remove();
        
        // 获取容器尺寸
        const containerRect = this.container.node().getBoundingClientRect();
        const width = containerRect.width;
        const height = containerRect.height;
        
        // 创建SVG
        this.svg = this.container
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('background', 'transparent');
        
        // 创建缩放行为
        this.zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                this.transform = event.transform;
                this.g.attr('transform', this.transform);
            });
        
        this.svg.call(this.zoom);
        
        // 创建主绘图组
        this.g = this.svg.append('g');
        
        // 创建箭头标记
        this.createArrowMarkers();
        
        // 初始化力导向模拟
        this.simulation = d3.forceSimulation()
            .force('link', d3.forceLink().id(d => d.id).distance(CONFIG.GRAPH.LINK_DISTANCE))
            .force('charge', d3.forceManyBody().strength(CONFIG.GRAPH.CHARGE_STRENGTH))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 5));
    }
    
    // 创建箭头标记
    createArrowMarkers() {
        const defs = this.svg.append('defs');
        
        Object.entries(CONFIG.LINK_TYPES).forEach(([type, config]) => {
            defs.append('marker')
                .attr('id', `arrow-${type.toLowerCase()}`)
                .attr('viewBox', '0 -5 10 10')
                .attr('refX', 15)
                .attr('refY', 0)
                .attr('markerWidth', 6)
                .attr('markerHeight', 6)
                .attr('orient', 'auto')
                .append('path')
                .attr('d', 'M0,-5L10,0L0,5')
                .attr('fill', config.color);
        });
    }
    
    // 渲染图谱数据
    renderGraph(data) {
        this.nodes = [...data.nodes];
        this.links = [...data.links];
        
        // 隐藏欢迎消息
        this.container.select('.welcome-message').style('display', 'none');
        
        // 更新力导向模拟
        this.simulation.nodes(this.nodes);
        this.simulation.force('link').links(this.links);
        
        // 渲染链接
        this.renderLinks();
        
        // 渲染节点
        this.renderNodes();
        
        // 重启模拟
        this.simulation.alpha(1).restart();
        
        // 自动适应视图
        setTimeout(() => this.fitToView(), 1000);
    }
    
    // 渲染链接
    renderLinks() {
        const linkGroup = this.g.selectAll('.link-group')
            .data(this.links, d => `${d.source.id || d.source}-${d.target.id || d.target}`);
        
        linkGroup.exit().remove();
        
        const linkEnter = linkGroup.enter()
            .append('g')
            .attr('class', 'link-group');
        
        linkEnter.append('line')
            .attr('class', 'link')
            .attr('stroke', d => CONFIG.LINK_TYPES[d.type]?.color || '#999')
            .attr('stroke-width', d => CONFIG.LINK_TYPES[d.type]?.strokeWidth || 1)
            .attr('stroke-dasharray', d => CONFIG.LINK_TYPES[d.type]?.strokeDasharray || 'none')
            .attr('marker-end', d => `url(#arrow-${d.type.toLowerCase()})`);
        
        this.linkElements = linkEnter.merge(linkGroup).select('.link');
    }
    
    // 渲染节点
    renderNodes() {
        const nodeGroup = this.g.selectAll('.node-group')
            .data(this.nodes, d => d.id);
        
        nodeGroup.exit().remove();
        
        const nodeEnter = nodeGroup.enter()
            .append('g')
            .attr('class', 'node-group')
            .style('cursor', 'pointer')
            .call(this.createDragBehavior());
        
        // 添加节点圆圈
        nodeEnter.append('circle')
            .attr('class', 'node')
            .attr('r', d => this.getNodeRadius(d))
            .attr('fill', d => this.getNodeColor(d))
            .attr('stroke', '#fff')
            .attr('stroke-width', d => CONFIG.NODE_TYPES[d.type]?.strokeWidth || 2);
        
        // 添加节点标签
        nodeEnter.append('text')
            .attr('class', 'node-label')
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .style('font-size', d => `${Math.max(10, this.getNodeRadius(d) / 3)}px`)
            .style('font-weight', '500')
            .style('fill', '#fff')
            .style('pointer-events', 'none')
            .text(d => this.truncateText(d.name, this.getNodeRadius(d)));
        
        // 添加节点标题（悬停显示完整名称）
        nodeEnter.append('title')
            .text(d => d.name);
        
        this.nodeElements = nodeEnter.merge(nodeGroup);
        
        // 添加点击事件
        this.nodeElements.on('click', (event, d) => {
            event.stopPropagation();
            this.onNodeClick(d);
        });
        
        // 更新模拟的tick事件
        this.simulation.on('tick', () => this.tick());
    }
    
    // 获取节点半径
    getNodeRadius(node) {
        const baseRadius = CONFIG.NODE_TYPES[node.type]?.radius || CONFIG.GRAPH.NODE_RADIUS.DEFAULT;
        return baseRadius * (0.5 + node.importance * 0.5);
    }
    
    // 获取节点颜色
    getNodeColor(node) {
        return CONFIG.NODE_TYPES[node.type]?.color || CONFIG.GRAPH.COLORS.PRIMARY;
    }
    
    // 截断文本
    truncateText(text, radius) {
        const maxLength = Math.floor(radius / 4);
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    // 创建拖拽行为
    createDragBehavior() {
        return d3.drag()
            .on('start', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', (event, d) => {
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            });
    }
    
    // 模拟tick事件
    tick() {
        if (this.linkElements) {
            this.linkElements
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);
        }
        
        if (this.nodeElements) {
            this.nodeElements
                .attr('transform', d => `translate(${d.x},${d.y})`);
        }
    }
    
    // 节点点击事件
    onNodeClick(node) {
        // 高亮选中的节点
        this.highlightNode(node);
        
        // 触发侧边栏显示
        if (window.sidebar) {
            window.sidebar.showNodeDetails(node);
        }
    }
    
    // 高亮节点
    highlightNode(selectedNode) {
        this.nodeElements.select('.node')
            .attr('stroke', d => d.id === selectedNode.id ? '#fbbf24' : '#fff')
            .attr('stroke-width', d => d.id === selectedNode.id ? 4 : 2);
    }
    
    // 适应视图
    fitToView() {
        if (this.nodes.length === 0) return;
        
        const bounds = this.getBounds();
        const width = this.svg.attr('width');
        const height = this.svg.attr('height');
        
        const scale = Math.min(
            width / (bounds.width + 100),
            height / (bounds.height + 100),
            2
        );
        
        const translate = [
            width / 2 - scale * bounds.centerX,
            height / 2 - scale * bounds.centerY
        ];
        
        this.svg.transition()
            .duration(750)
            .call(this.zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
    }
    
    // 获取图谱边界
    getBounds() {
        const xs = this.nodes.map(d => d.x);
        const ys = this.nodes.map(d => d.y);
        
        const minX = Math.min(...xs);
        const maxX = Math.max(...xs);
        const minY = Math.min(...ys);
        const maxY = Math.max(...ys);
        
        return {
            minX, maxX, minY, maxY,
            width: maxX - minX,
            height: maxY - minY,
            centerX: (minX + maxX) / 2,
            centerY: (minY + maxY) / 2
        };
    }
    
    // 缩放控制
    zoomIn() {
        this.svg.transition().call(this.zoom.scaleBy, 1.5);
    }
    
    zoomOut() {
        this.svg.transition().call(this.zoom.scaleBy, 1 / 1.5);
    }
    
    resetView() {
        this.fitToView();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    // 处理窗口大小变化
    handleResize() {
        const containerRect = this.container.node().getBoundingClientRect();
        const width = containerRect.width;
        const height = containerRect.height;
        
        this.svg
            .attr('width', width)
            .attr('height', height);
        
        this.simulation
            .force('center', d3.forceCenter(width / 2, height / 2))
            .alpha(0.3)
            .restart();
    }
    
    // 清除图谱
    clear() {
        this.g.selectAll('*').remove();
        this.nodes = [];
        this.links = [];
        this.container.select('.welcome-message').style('display', 'block');
    }
}

// 创建全局图谱实例
let graphVisualization = null;
