// 侧边栏模块 - 负责显示概念详情
class Sidebar {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.sidebarTitle = document.getElementById('sidebarTitle');
        this.sidebarContent = document.getElementById('sidebarContent');
        this.closeSidebarBtn = document.getElementById('closeSidebar');
        this.currentData = null;
        this.currentNode = null;
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 关闭侧边栏
        this.closeSidebarBtn.addEventListener('click', () => {
            this.hide();
        });
        
        // 点击侧边栏外部关闭
        document.addEventListener('click', (event) => {
            if (this.sidebar.classList.contains('open') && 
                !this.sidebar.contains(event.target) && 
                !event.target.closest('.node-group')) {
                this.hide();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.sidebar.classList.contains('open')) {
                this.hide();
            }
        });
    }
    
    // 显示节点详情
    showNodeDetails(node) {
        this.currentNode = node;
        
        // 获取节点详细信息
        const details = this.getNodeDetails(node);
        
        // 更新标题
        this.sidebarTitle.textContent = node.name;
        
        // 渲染内容
        this.renderContent(details);
        
        // 显示侧边栏
        this.show();
    }
    
    // 获取节点详细信息
    getNodeDetails(node) {
        // 从当前数据中获取详情
        if (this.currentData && this.currentData.details && this.currentData.details[node.id]) {
            return this.currentData.details[node.id];
        }
        
        // 返回默认详情
        return this.generateDefaultDetails(node);
    }
    
    // 生成默认详情
    generateDefaultDetails(node) {
        const typeDescriptions = {
            'MAIN_CONCEPT': '这是一个核心哲学概念',
            'SUB_CONCEPT': '这是一个重要的子概念',
            'RELATED_CONCEPT': '这是一个相关概念',
            'PHILOSOPHER': '这是一位重要的哲学家',
            'WORK': '这是一部重要的哲学著作'
        };
        
        return {
            title: node.name,
            description: `${node.name}是${typeDescriptions[node.type] || '一个哲学概念'}。`,
            keyPoints: [
                '这是一个重要的哲学概念',
                '在哲学史上具有重要地位',
                '影响了后续的哲学发展'
            ],
            philosophers: ['相关哲学家待补充'],
            works: ['相关著作待补充']
        };
    }
    
    // 渲染内容
    renderContent(details) {
        const content = `
            <div class="concept-details">
                <div class="concept-header">
                    <div class="concept-type">
                        <span class="type-badge type-${this.currentNode.type.toLowerCase()}">
                            ${this.getTypeDisplayName(this.currentNode.type)}
                        </span>
                        <span class="importance-score">
                            重要度: ${Math.round(this.currentNode.importance * 100)}%
                        </span>
                    </div>
                </div>
                
                <div class="concept-description">
                    <h4><i class="fas fa-info-circle"></i> 概念描述</h4>
                    <p>${details.description}</p>
                </div>
                
                <div class="key-points">
                    <h4><i class="fas fa-key"></i> 关键要点</h4>
                    <ul>
                        ${details.keyPoints.map(point => `<li>${point}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="related-philosophers">
                    <h4><i class="fas fa-user-graduate"></i> 相关哲学家</h4>
                    <div class="tag-list">
                        ${details.philosophers.map(philosopher => 
                            `<span class="tag philosopher-tag">${philosopher}</span>`
                        ).join('')}
                    </div>
                </div>
                
                <div class="related-works">
                    <h4><i class="fas fa-book"></i> 相关著作</h4>
                    <div class="tag-list">
                        ${details.works.map(work => 
                            `<span class="tag work-tag">${work}</span>`
                        ).join('')}
                    </div>
                </div>
                
                <div class="concept-connections">
                    <h4><i class="fas fa-project-diagram"></i> 概念关系</h4>
                    <div class="connections-list">
                        ${this.renderConnections()}
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn primary" onclick="sidebar.exploreRelated()">
                        <i class="fas fa-search-plus"></i>
                        深入探索
                    </button>
                    <button class="action-btn secondary" onclick="sidebar.addToFavorites()">
                        <i class="fas fa-heart"></i>
                        收藏概念
                    </button>
                </div>
            </div>
        `;
        
        this.sidebarContent.innerHTML = content;
    }
    
    // 获取类型显示名称
    getTypeDisplayName(type) {
        const typeNames = {
            'MAIN_CONCEPT': '核心概念',
            'SUB_CONCEPT': '子概念',
            'RELATED_CONCEPT': '相关概念',
            'PHILOSOPHER': '哲学家',
            'WORK': '著作'
        };
        return typeNames[type] || type;
    }
    
    // 渲染概念关系
    renderConnections() {
        if (!this.currentData || !this.currentData.links) {
            return '<p class="no-connections">暂无关系数据</p>';
        }
        
        const connections = this.currentData.links.filter(link => 
            link.source.id === this.currentNode.id || link.target.id === this.currentNode.id
        );
        
        if (connections.length === 0) {
            return '<p class="no-connections">暂无直接关系</p>';
        }
        
        return connections.map(link => {
            const isSource = link.source.id === this.currentNode.id;
            const relatedNode = isSource ? link.target : link.source;
            const relationshipType = this.getRelationshipDisplayName(link.type);
            const direction = isSource ? '→' : '←';
            
            return `
                <div class="connection-item" onclick="sidebar.navigateToNode('${relatedNode.id}')">
                    <span class="connection-direction">${direction}</span>
                    <span class="connection-type">${relationshipType}</span>
                    <span class="connection-target">${relatedNode.name}</span>
                </div>
            `;
        }).join('');
    }
    
    // 获取关系类型显示名称
    getRelationshipDisplayName(type) {
        const relationshipNames = {
            'CONTAINS': '包含',
            'RELATES_TO': '相关',
            'OPPOSES': '对立',
            'INFLUENCES': '影响'
        };
        return relationshipNames[type] || type;
    }
    
    // 导航到其他节点
    navigateToNode(nodeId) {
        if (!this.currentData || !this.currentData.nodes) return;
        
        const targetNode = this.currentData.nodes.find(node => node.id === nodeId);
        if (targetNode) {
            this.showNodeDetails(targetNode);
            
            // 在图谱中高亮该节点
            if (window.graphVisualization) {
                window.graphVisualization.highlightNode(targetNode);
            }
        }
    }
    
    // 深入探索相关概念
    exploreRelated() {
        if (!this.currentNode) return;
        
        // 这里可以触发新的搜索，基于当前概念
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = this.currentNode.name;
            // 触发搜索事件
            const searchEvent = new Event('input', { bubbles: true });
            searchInput.dispatchEvent(searchEvent);
        }
        
        this.showMessage('正在探索相关概念...', 'info');
    }
    
    // 添加到收藏
    addToFavorites() {
        if (!this.currentNode) return;
        
        // 这里可以实现收藏功能
        const favorites = JSON.parse(localStorage.getItem('philosophy_favorites') || '[]');
        
        if (!favorites.find(item => item.id === this.currentNode.id)) {
            favorites.push({
                id: this.currentNode.id,
                name: this.currentNode.name,
                type: this.currentNode.type,
                timestamp: Date.now()
            });
            
            localStorage.setItem('philosophy_favorites', JSON.stringify(favorites));
            this.showMessage('已添加到收藏', 'success');
        } else {
            this.showMessage('该概念已在收藏中', 'warning');
        }
    }
    
    // 显示消息
    showMessage(message, type = 'info') {
        // 创建临时消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `sidebar-message ${type}`;
        messageEl.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation' : 'info'}"></i>
            ${message}
        `;
        
        // 插入到侧边栏顶部
        this.sidebarContent.insertBefore(messageEl, this.sidebarContent.firstChild);
        
        // 3秒后移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }
    
    // 显示侧边栏
    show() {
        this.sidebar.classList.add('open');
    }
    
    // 隐藏侧边栏
    hide() {
        this.sidebar.classList.remove('open');
        
        // 清除图谱中的高亮
        if (window.graphVisualization && window.graphVisualization.nodeElements) {
            window.graphVisualization.nodeElements.select('.node')
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
        }
    }
    
    // 设置当前数据
    setCurrentData(data) {
        this.currentData = data;
    }
    
    // 检查是否打开
    isOpen() {
        return this.sidebar.classList.contains('open');
    }
}

// 创建全局侧边栏实例
const sidebar = new Sidebar();
