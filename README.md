# 哲学知识图谱学习网站

一个基于AI的交互式哲学概念学习平台，通过可视化知识图谱帮助用户深入理解哲学概念及其关系。

## 🌟 核心功能

### 🔍 智能主题分析
- 输入任何哲学主题（如"存在主义"、"道德哲学"、"自由意志"等）
- AI自动分析并提取核心概念、子概念和相关理论
- 生成结构化的概念关系网络

### 📊 交互式知识图谱
- 动态可视化展示概念间的逻辑关系
- 节点大小反映概念的重要程度
- 支持缩放、拖拽、平移等交互操作
- 不同颜色区分概念类型（核心概念、子概念、哲学家、著作等）

### 📚 详细内容展示
- 点击任意概念节点查看详细信息
- 包含概念描述、关键要点、相关哲学家、经典著作
- 显示概念间的关系类型（包含、相关、对立、影响等）
- 支持概念间的快速导航

### 🎨 现代化用户界面
- 响应式设计，完美适配桌面和移动设备
- 优雅的渐变背景和毛玻璃效果
- 流畅的动画过渡和交互反馈
- 直观的操作界面和控制面板

## 🚀 快速开始

### 在线体验
直接打开 `index.html` 文件即可在浏览器中使用，无需安装任何依赖。

### 本地运行
1. 克隆或下载项目文件
2. 使用现代浏览器打开 `index.html`
3. 开始探索哲学世界！

## 📖 使用指南

### 基本操作
1. **搜索哲学主题**：在顶部搜索框输入感兴趣的哲学概念
2. **查看知识图谱**：AI分析完成后，图谱将自动显示概念关系
3. **探索概念详情**：点击任意节点查看详细信息
4. **图谱交互**：
   - 鼠标滚轮：缩放图谱
   - 拖拽：移动视图
   - 拖拽节点：调整节点位置

### 快捷键
- `Ctrl/Cmd + K`：聚焦搜索框
- `Ctrl/Cmd + Enter`：执行搜索
- `F11`：切换全屏模式
- `ESC`：关闭侧边栏

### 示例主题
- **存在主义**：探索萨特、加缪等思想家的核心理念
- **道德哲学**：了解德性伦理学、义务伦理学、后果主义
- **自由意志**：研究决定论与自由选择的哲学争论
- **认识论**：深入理解知识、真理、信念的本质

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**：语义化标记和现代Web标准
- **CSS3**：响应式设计、Flexbox布局、CSS动画
- **JavaScript ES6+**：模块化架构、异步编程
- **D3.js v7**：数据驱动的图形可视化
- **Font Awesome**：图标库
- **Google Fonts**：Inter字体

### 项目结构
```
philosophy-knowledge-graph/
├── index.html              # 主页面
├── styles.css             # 样式文件
├── js/
│   ├── config.js          # 配置文件
│   ├── ai-service.js      # AI服务模块
│   ├── graph-visualization.js  # 图谱可视化
│   ├── sidebar.js         # 侧边栏组件
│   └── app.js            # 主应用逻辑
└── README.md             # 项目说明
```

### 核心模块

#### AI服务模块 (`ai-service.js`)
- 负责哲学概念的智能分析
- 目前使用精心设计的模拟数据
- 预留真实AI API集成接口

#### 图谱可视化 (`graph-visualization.js`)
- 基于D3.js的力导向图布局
- 支持节点拖拽、缩放、平移
- 动态渲染和交互响应

#### 侧边栏组件 (`sidebar.js`)
- 概念详情展示
- 关系网络导航
- 收藏和探索功能

#### 主应用 (`app.js`)
- 协调各模块交互
- 事件处理和状态管理
- 用户体验优化

## 🎯 设计特色

### 视觉设计
- **渐变背景**：营造深邃的思考氛围
- **毛玻璃效果**：现代化的界面层次
- **动态交互**：流畅的动画和过渡效果
- **色彩系统**：语义化的颜色编码

### 交互设计
- **直观操作**：符合用户习惯的交互模式
- **即时反馈**：实时的视觉和状态反馈
- **渐进式披露**：分层展示信息，避免认知负担
- **无障碍设计**：支持键盘导航和屏幕阅读器

### 信息架构
- **概念层次**：清晰的主概念-子概念结构
- **关系类型**：包含、相关、对立、影响等语义关系
- **重要度权重**：基于哲学史重要性的节点大小
- **多维度信息**：概念、人物、著作的综合展示

## 🔧 自定义配置

### 修改配置
编辑 `js/config.js` 文件可以自定义：
- 图谱布局参数
- 节点和链接样式
- 颜色主题
- 动画效果

### 添加数据
在 `js/ai-service.js` 的 `mockData` 中添加新的哲学主题数据。

### 样式定制
修改 `styles.css` 中的CSS变量和类样式来调整界面外观。

## 🌐 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📝 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目！

### 开发建议
1. 保持代码风格一致
2. 添加适当的注释
3. 测试新功能的兼容性
4. 更新相关文档

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**探索哲学的智慧世界，让思想在知识图谱中自由连接！** 🧠✨
