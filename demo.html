<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哲学知识图谱 - 功能演示</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #4f46e5;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #4f46e5;
        }
        
        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }
        
        .feature-description {
            color: #6b7280;
            margin-bottom: 1rem;
        }
        
        .demo-steps {
            background: #f8fafc;
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .demo-steps h3 {
            color: #4f46e5;
            margin-bottom: 1rem;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .step-number {
            background: #4f46e5;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #1f2937;
        }
        
        .step-description {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .launch-btn {
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 2rem auto;
            padding: 1rem 2rem;
            background: #4f46e5;
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        
        .launch-btn:hover {
            background: #3730a3;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .tech-tag {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.75rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .demo-container {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="demo-container">
        <h1><i class="fas fa-brain"></i> 哲学知识图谱学习网站</h1>
        
        <p style="text-align: center; font-size: 1.1rem; color: #6b7280; margin-bottom: 2rem;">
            基于AI的交互式哲学概念学习平台，通过可视化知识图谱帮助您深入理解哲学概念及其关系
        </p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-search"></i></div>
                <div class="feature-title">智能主题分析</div>
                <div class="feature-description">
                    输入任何哲学主题，AI自动分析并提取核心概念、子概念和相关理论，生成结构化的概念关系网络。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-project-diagram"></i></div>
                <div class="feature-title">交互式知识图谱</div>
                <div class="feature-description">
                    动态可视化展示概念间的逻辑关系，支持缩放、拖拽、平移等交互操作，节点大小反映重要程度。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-book-open"></i></div>
                <div class="feature-title">详细内容展示</div>
                <div class="feature-description">
                    点击任意概念节点查看详细信息，包含概念描述、关键要点、相关哲学家、经典著作等。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
                <div class="feature-title">响应式设计</div>
                <div class="feature-description">
                    完美适配桌面和移动设备，优雅的界面设计和流畅的动画过渡，提供最佳用户体验。
                </div>
            </div>
        </div>
        
        <div class="demo-steps">
            <h3><i class="fas fa-play-circle"></i> 使用步骤</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">输入哲学主题</div>
                    <div class="step-description">在搜索框中输入感兴趣的哲学概念，如"存在主义"、"道德哲学"、"自由意志"等</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">AI智能分析</div>
                    <div class="step-description">系统自动分析主题，提取相关概念、哲学家、著作，并建立概念间的关系网络</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">探索知识图谱</div>
                    <div class="step-description">查看动态生成的知识图谱，通过缩放、拖拽等方式探索概念关系</div>
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">深入学习</div>
                    <div class="step-description">点击任意节点查看详细信息，了解概念内涵、代表人物、经典著作等</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: #4f46e5; margin-bottom: 1rem;">技术栈</h3>
            <div class="tech-stack" style="justify-content: center;">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript ES6+</span>
                <span class="tech-tag">D3.js</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">AI集成</span>
            </div>
        </div>
        
        <a href="index.html" class="launch-btn">
            <i class="fas fa-rocket"></i> 开始探索哲学世界
        </a>
        
        <div style="text-align: center; margin-top: 2rem; color: #6b7280; font-size: 0.875rem;">
            <p>💡 提示：尝试搜索"存在主义"、"道德哲学"、"自由意志"、"认识论"等主题</p>
        </div>
    </div>
</body>
</html>
